-- WisCude 后台管理系统 - 数据库初始化脚本
-- PostgreSQL 15+

-- 创建数据库（如果不存在）
-- CREATE DATABASE wiscude_admin OWNER postgres;

-- 连接到数据库
-- \c wiscude_admin;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建7位数字用户ID序列和生成函数
CREATE SEQUENCE IF NOT EXISTS user_id_seq START 1000000;

-- 删除现有函数（如果存在）
DROP FUNCTION IF EXISTS generate_user_id();

CREATE FUNCTION generate_user_id() RETURNS INTEGER AS $$
BEGIN
    RETURN nextval('user_id_seq');
END;
$$ LANGUAGE plpgsql;

-- 创建枚举类型
CREATE TYPE membership_type AS ENUM ('REGULAR', 'VIP');
CREATE TYPE user_level AS ENUM ('LEVEL_ONE', 'LEVEL_TWO', 'LEVEL_THREE', 'LEVEL_FOUR', 'LEVEL_FIVE', 'LEVEL_SIX');
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED');
CREATE TYPE gender_type AS ENUM ('MALE', 'FEMALE', 'OTHER', 'UNKNOWN');
CREATE TYPE question_type AS ENUM ('single_choice', 'multiple_choice', 'fill_blank', 'essay', 'true_false');
CREATE TYPE review_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE banner_status AS ENUM ('draft', 'active', 'inactive', 'expired');
CREATE TYPE course_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE course_level AS ENUM ('beginner', 'elementary', 'intermediate', 'advanced');
CREATE TYPE mentorship_status AS ENUM ('pending', 'active', 'completed', 'cancelled');
CREATE TYPE activity_status AS ENUM ('draft', 'published', 'ongoing', 'completed', 'cancelled');
CREATE TYPE post_status AS ENUM ('draft', 'published', 'hidden', 'deleted');

-- 管理员角色枚举
CREATE TYPE admin_role AS ENUM ('SUPERADMIN', 'ADMIN', 'EDITOR', 'VIEWER');

-- 管理员表（与后端ORM模型保持一致）
CREATE TABLE IF NOT EXISTS admin_users (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(100) NOT NULL,
    full_name VARCHAR(100),
    role admin_role DEFAULT 'VIEWER' NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_superuser BOOLEAN DEFAULT false NOT NULL,
    last_login TIMESTAMP,
    avatar_url VARCHAR(255),
    bio TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 保留原admin表作为别名视图（向后兼容）
CREATE OR REPLACE VIEW admin AS SELECT * FROM admin_users;

-- 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id VARCHAR(36),
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(50),
    user_agent VARCHAR(255),
    status BOOLEAN DEFAULT true NOT NULL,
    message VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id VARCHAR(36),
    username VARCHAR(50),
    action VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,
    level VARCHAR(20) DEFAULT 'INFO' NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    ip_address VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 用户表
CREATE TABLE IF NOT EXISTS "user" (
    id INTEGER PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    nickname VARCHAR(50) NOT NULL,
    hashed_password VARCHAR(100) NOT NULL,
    full_name VARCHAR(100),
    gender gender_type DEFAULT 'UNKNOWN' NOT NULL,
    status user_status DEFAULT 'ACTIVE' NOT NULL,
    membership membership_type DEFAULT 'REGULAR' NOT NULL,
    level user_level DEFAULT 'LEVEL_ONE' NOT NULL,
    registration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    birth_date DATE,
    avatar_url VARCHAR(255),
    bio TEXT,
    location VARCHAR(100),
    is_premium BOOLEAN DEFAULT false NOT NULL,
    premium_expires_at TIMESTAMP,
    last_login TIMESTAMP,
    total_study_time INTEGER DEFAULT 0 NOT NULL,
    total_focus_sessions INTEGER DEFAULT 0 NOT NULL,
    total_check_ins INTEGER DEFAULT 0 NOT NULL,
    experience_points INTEGER DEFAULT 0 NOT NULL,
    settings JSONB,
    preferences JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 为用户表ID字段设置默认值
ALTER TABLE "user" ALTER COLUMN id SET DEFAULT generate_user_id();

-- 学习会话表
CREATE TABLE IF NOT EXISTS study_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_session_id VARCHAR(36) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    session_type VARCHAR(50) NOT NULL,
    title VARCHAR(100),
    subject VARCHAR(100),
    duration INTEGER NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    interruptions INTEGER DEFAULT 0 NOT NULL,
    productivity_score FLOAT,
    notes TEXT,
    is_completed BOOLEAN DEFAULT false NOT NULL,
    completion_percentage FLOAT DEFAULT 0 NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 每日打卡表
CREATE TABLE IF NOT EXISTS check_ins (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_checkin_id VARCHAR(36) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    mood VARCHAR(20),
    energy_level INTEGER,
    study_goals TEXT,
    reflection TEXT,
    goals_completed INTEGER DEFAULT 0 NOT NULL,
    study_time_today INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(user_id, date)
);

-- AI学习记录表
CREATE TABLE IF NOT EXISTS ai_learning_records (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_record_id VARCHAR(36) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    topic VARCHAR(100) NOT NULL,
    difficulty VARCHAR(20),
    duration INTEGER NOT NULL,
    questions_count INTEGER DEFAULT 0 NOT NULL,
    correct_answers INTEGER DEFAULT 0 NOT NULL,
    content JSONB,
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 社区帖子表
CREATE TABLE IF NOT EXISTS community_posts (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_post_id VARCHAR(36) UNIQUE NOT NULL,
    author_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    post_type VARCHAR(20) DEFAULT 'discussion' NOT NULL,
    category VARCHAR(50),
    tags JSONB,
    images JSONB,
    attachments JSONB,
    likes_count INTEGER DEFAULT 0 NOT NULL,
    comments_count INTEGER DEFAULT 0 NOT NULL,
    views_count INTEGER DEFAULT 0 NOT NULL,
    shares_count INTEGER DEFAULT 0 NOT NULL,
    status VARCHAR(20) DEFAULT 'published' NOT NULL,
    is_featured BOOLEAN DEFAULT false NOT NULL,
    is_pinned BOOLEAN DEFAULT false NOT NULL,
    reviewed_by VARCHAR(36),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 帖子评论表
CREATE TABLE IF NOT EXISTS post_comments (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_comment_id VARCHAR(36) UNIQUE NOT NULL,
    post_id VARCHAR(36) NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
    author_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    parent_id VARCHAR(36) REFERENCES post_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    likes_count INTEGER DEFAULT 0 NOT NULL,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程表
CREATE TABLE IF NOT EXISTS courses (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_course_id VARCHAR(36) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    instructor VARCHAR(100),
    category VARCHAR(50),
    difficulty VARCHAR(20),
    duration INTEGER,
    chapters_count INTEGER DEFAULT 0 NOT NULL,
    price FLOAT DEFAULT 0.0 NOT NULL,
    original_price FLOAT,
    is_free BOOLEAN DEFAULT true NOT NULL,
    is_featured BOOLEAN DEFAULT false NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_new BOOLEAN DEFAULT false NOT NULL,
    is_hot BOOLEAN DEFAULT false NOT NULL,
    is_premium BOOLEAN DEFAULT false NOT NULL,
    enrollment_count INTEGER DEFAULT 0 NOT NULL,
    completion_count INTEGER DEFAULT 0 NOT NULL,
    rating FLOAT DEFAULT 0.0 NOT NULL,
    review_count INTEGER DEFAULT 0 NOT NULL,
    cover_image VARCHAR(255),
    preview_video VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程注册表
CREATE TABLE IF NOT EXISTS course_enrollments (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    android_enrollment_id VARCHAR(36) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    course_id VARCHAR(36) NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    progress_percentage FLOAT DEFAULT 0.0 NOT NULL,
    completed_chapters INTEGER DEFAULT 0 NOT NULL,
    last_accessed TIMESTAMP,
    is_completed BOOLEAN DEFAULT false NOT NULL,
    completed_at TIMESTAMP,
    rating INTEGER,
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(user_id, course_id)
);

-- 数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    sync_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(50),
    status VARCHAR(20) NOT NULL,
    records_processed INTEGER DEFAULT 0 NOT NULL,
    records_inserted INTEGER DEFAULT 0 NOT NULL,
    records_updated INTEGER DEFAULT 0 NOT NULL,
    records_failed INTEGER DEFAULT 0 NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    duration_seconds INTEGER,
    error_message TEXT,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 广告管理模块 ====================

-- 轮播图表
CREATE TABLE IF NOT EXISTS banners (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    image_url VARCHAR(500) NOT NULL,
    link_url VARCHAR(500),
    position VARCHAR(50) NOT NULL DEFAULT 'home', -- home, course, community
    sort_order INTEGER DEFAULT 0,
    status banner_status DEFAULT 'draft' NOT NULL,
    view_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    click_rate DECIMAL(5,4) DEFAULT 0.0000,
    publish_start_time TIMESTAMP,
    publish_end_time TIMESTAMP,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 弹窗广告表
CREATE TABLE IF NOT EXISTS popups (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    image_url VARCHAR(500),
    link_url VARCHAR(500),
    popup_type VARCHAR(50) DEFAULT 'modal', -- modal, toast, banner
    target_audience JSONB, -- 目标用户群体配置
    display_rules JSONB, -- 显示规则配置
    status banner_status DEFAULT 'draft' NOT NULL,
    view_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    close_count INTEGER DEFAULT 0,
    publish_start_time TIMESTAMP,
    publish_end_time TIMESTAMP,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 题库管理模块 ====================

-- 题目分类表
CREATE TABLE IF NOT EXISTS question_categories (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id VARCHAR(36) REFERENCES question_categories(id),
    subject VARCHAR(50) NOT NULL, -- 学科
    grade_level VARCHAR(50), -- 年级
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 题目表
CREATE TABLE IF NOT EXISTS questions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    question_type question_type NOT NULL,
    category_id VARCHAR(36) REFERENCES question_categories(id),
    subject VARCHAR(50) NOT NULL,
    grade_level VARCHAR(50),
    difficulty INTEGER DEFAULT 1 CHECK (difficulty >= 1 AND difficulty <= 5),
    knowledge_points TEXT[], -- 知识点数组
    score DECIMAL(5,2) DEFAULT 1.0,
    time_limit INTEGER DEFAULT 0, -- 答题时间限制(秒)
    options JSONB, -- 选择题选项
    correct_answer TEXT,
    answer_analysis TEXT,
    images TEXT[], -- 图片URL数组
    audio_url VARCHAR(500),
    video_url VARCHAR(500),
    status post_status DEFAULT 'draft' NOT NULL,
    usage_count INTEGER DEFAULT 0,
    correct_rate DECIMAL(5,4) DEFAULT 0.0000,
    quality_score DECIMAL(3,2) DEFAULT 0.00,
    review_status review_status DEFAULT 'pending' NOT NULL,
    reviewed_by INTEGER REFERENCES "user"(id),
    reviewed_at TIMESTAMP,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 试卷表
CREATE TABLE IF NOT EXISTS exam_papers (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    subject VARCHAR(50) NOT NULL,
    grade_level VARCHAR(50),
    total_score DECIMAL(6,2) DEFAULT 0.00,
    time_limit INTEGER DEFAULT 0, -- 考试时间限制(分钟)
    question_count INTEGER DEFAULT 0,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    status post_status DEFAULT 'draft' NOT NULL,
    is_template BOOLEAN DEFAULT false, -- 是否为模板
    usage_count INTEGER DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0.00,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 试卷题目关联表
CREATE TABLE IF NOT EXISTS exam_paper_questions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    paper_id VARCHAR(36) REFERENCES exam_papers(id) ON DELETE CASCADE,
    question_id VARCHAR(36) REFERENCES questions(id) ON DELETE CASCADE,
    question_order INTEGER NOT NULL,
    question_score DECIMAL(5,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(paper_id, question_id)
);

-- ==================== 课程管理模块 ====================

-- 课程分类表
CREATE TABLE IF NOT EXISTS course_categories (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id VARCHAR(36) REFERENCES course_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 讲师表
CREATE TABLE IF NOT EXISTS instructors (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    name VARCHAR(100) NOT NULL,
    bio TEXT,
    avatar_url VARCHAR(500),
    specialties TEXT[], -- 专业领域
    experience_years INTEGER DEFAULT 0,
    education_background TEXT,
    certifications TEXT[],
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_courses INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    status user_status DEFAULT 'active' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程表(更新现有表)
CREATE TABLE IF NOT EXISTS courses_new (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    cover_image VARCHAR(500),
    category_id VARCHAR(36) REFERENCES course_categories(id),
    instructor_id VARCHAR(36) REFERENCES instructors(id),
    level course_level DEFAULT 'beginner' NOT NULL,
    price DECIMAL(10,2) DEFAULT 0.00,
    original_price DECIMAL(10,2),
    duration_hours DECIMAL(5,2) DEFAULT 0.00,
    lesson_count INTEGER DEFAULT 0,
    enrollment_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    status course_status DEFAULT 'draft' NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    is_free BOOLEAN DEFAULT false,
    prerequisites TEXT[],
    learning_objectives TEXT[],
    target_audience TEXT,
    language VARCHAR(50) DEFAULT 'zh-CN',
    tags TEXT[],
    published_at TIMESTAMP,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程章节表
CREATE TABLE IF NOT EXISTS course_chapters (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    course_id VARCHAR(36) REFERENCES courses_new(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    chapter_order INTEGER NOT NULL,
    duration_minutes INTEGER DEFAULT 0,
    is_free BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程课时表
CREATE TABLE IF NOT EXISTS course_lessons (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    chapter_id VARCHAR(36) REFERENCES course_chapters(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    lesson_type VARCHAR(50) DEFAULT 'video', -- video, audio, text, quiz
    video_url VARCHAR(500),
    audio_url VARCHAR(500),
    duration_minutes INTEGER DEFAULT 0,
    lesson_order INTEGER NOT NULL,
    is_free BOOLEAN DEFAULT false,
    resources JSONB, -- 课程资源
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 课程评价表
CREATE TABLE IF NOT EXISTS course_reviews (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    course_id VARCHAR(36) REFERENCES courses_new(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES "user"(id),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_anonymous BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    status post_status DEFAULT 'published' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(course_id, user_id)
);

-- 学习进度表
CREATE TABLE IF NOT EXISTS learning_progress (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    course_id VARCHAR(36) REFERENCES courses_new(id) ON DELETE CASCADE,
    lesson_id VARCHAR(36) REFERENCES course_lessons(id) ON DELETE CASCADE,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    watch_time_seconds INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(user_id, lesson_id)
);

-- ==================== 社区管理模块 ====================

-- 话题表
CREATE TABLE IF NOT EXISTS topics (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(500),
    color VARCHAR(20),
    post_count INTEGER DEFAULT 0,
    follower_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_hot BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 部落表
CREATE TABLE IF NOT EXISTS tribes (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    avatar VARCHAR(500),
    cover_image VARCHAR(500),
    member_count INTEGER DEFAULT 0,
    post_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT true,
    join_approval BOOLEAN DEFAULT false,
    rules TEXT,
    tags TEXT[],
    owner_id INTEGER REFERENCES "user"(id),
    status user_status DEFAULT 'active' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 部落成员表
CREATE TABLE IF NOT EXISTS tribe_members (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    tribe_id VARCHAR(36) REFERENCES tribes(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES "user"(id),
    role VARCHAR(50) DEFAULT 'member', -- owner, admin, moderator, member
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status user_status DEFAULT 'active' NOT NULL,
    UNIQUE(tribe_id, user_id)
);

-- 师徒关系表
CREATE TABLE IF NOT EXISTS mentorships (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    mentor_id INTEGER REFERENCES "user"(id),
    mentee_id INTEGER REFERENCES "user"(id),
    subject VARCHAR(100), -- 指导科目
    description TEXT,
    status mentorship_status DEFAULT 'pending' NOT NULL,
    start_date DATE,
    end_date DATE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(mentor_id, mentee_id, subject)
);

-- 活动竞赛表
CREATE TABLE IF NOT EXISTS activities (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    cover_image VARCHAR(500),
    activity_type VARCHAR(50) NOT NULL, -- competition, event, challenge
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    registration_start TIMESTAMP,
    registration_end TIMESTAMP,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    prize_info JSONB, -- 奖品信息
    rules TEXT,
    requirements TEXT,
    status activity_status DEFAULT 'draft' NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    organizer_id INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 活动参与表
CREATE TABLE IF NOT EXISTS activity_participants (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    activity_id VARCHAR(36) REFERENCES activities(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES "user"(id),
    registration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status user_status DEFAULT 'active' NOT NULL,
    score DECIMAL(10,2),
    rank INTEGER,
    submission_data JSONB, -- 提交的作品或答案
    UNIQUE(activity_id, user_id)
);

-- 心愿墙表
CREATE TABLE IF NOT EXISTS wishes (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    content TEXT NOT NULL,
    category VARCHAR(50), -- study, career, life, other
    is_anonymous BOOLEAN DEFAULT false,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    is_fulfilled BOOLEAN DEFAULT false,
    fulfilled_by INTEGER REFERENCES "user"(id),
    fulfilled_at TIMESTAMP,
    status post_status DEFAULT 'published' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 心理咨询模块 ====================

-- 咨询师表
CREATE TABLE IF NOT EXISTS counselors (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    name VARCHAR(100) NOT NULL,
    title VARCHAR(100), -- 职称
    bio TEXT,
    avatar_url VARCHAR(500),
    specialties TEXT[], -- 专业领域
    experience_years INTEGER DEFAULT 0,
    education_background TEXT,
    certifications TEXT[], -- 资质证书
    license_number VARCHAR(100), -- 执业证号
    rating DECIMAL(3,2) DEFAULT 0.00,
    consultation_count INTEGER DEFAULT 0,
    hourly_rate DECIMAL(8,2), -- 咨询费用(每小时)
    available_times JSONB, -- 可预约时间
    is_verified BOOLEAN DEFAULT false,
    status user_status DEFAULT 'active' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 咨询预约表
CREATE TABLE IF NOT EXISTS consultations (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    counselor_id VARCHAR(36) REFERENCES counselors(id),
    user_id INTEGER REFERENCES "user"(id),
    appointment_time TIMESTAMP NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    consultation_type VARCHAR(50) DEFAULT 'online', -- online, offline
    topic VARCHAR(200),
    description TEXT,
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, completed, cancelled, no_show
    fee DECIMAL(8,2),
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, refunded
    notes TEXT, -- 咨询师备注
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT, -- 用户反馈
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 系统配置模块 ====================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    category VARCHAR(100) NOT NULL, -- basic, security, notification, ai, etc.
    key VARCHAR(100) NOT NULL,
    value TEXT,
    value_type VARCHAR(50) DEFAULT 'string', -- string, number, boolean, json
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- 是否对前端公开
    is_editable BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    updated_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(category, key)
);

-- AI模型配置表
CREATE TABLE IF NOT EXISTS ai_models (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL, -- openai, anthropic, google, etc.
    model_id VARCHAR(100) NOT NULL,
    api_endpoint VARCHAR(500),
    api_key_encrypted TEXT, -- 加密存储的API密钥
    max_tokens INTEGER DEFAULT 4096,
    temperature DECIMAL(3,2) DEFAULT 0.70,
    description TEXT,
    capabilities TEXT[], -- text, image, audio, video
    pricing_info JSONB, -- 定价信息
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 学习资源模块 ====================

-- 学习资源表
CREATE TABLE IF NOT EXISTS learning_resources (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    resource_type VARCHAR(50) NOT NULL, -- document, video, audio, link, tool
    file_url VARCHAR(500),
    file_size BIGINT, -- 文件大小(字节)
    file_format VARCHAR(50), -- pdf, mp4, mp3, etc.
    thumbnail_url VARCHAR(500),
    category VARCHAR(100),
    tags TEXT[],
    subject VARCHAR(50),
    grade_level VARCHAR(50),
    difficulty INTEGER DEFAULT 1 CHECK (difficulty >= 1 AND difficulty <= 5),
    download_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    is_free BOOLEAN DEFAULT true,
    price DECIMAL(8,2) DEFAULT 0.00,
    status post_status DEFAULT 'published' NOT NULL,
    uploaded_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 自习室管理模块 ====================

-- 自习室表
CREATE TABLE IF NOT EXISTS study_rooms (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    room_type VARCHAR(50) DEFAULT 'public', -- public, private, group
    max_capacity INTEGER DEFAULT 50,
    current_users INTEGER DEFAULT 0,
    background_music VARCHAR(500), -- 背景音乐URL
    background_image VARCHAR(500), -- 背景图片URL
    rules TEXT,
    is_active BOOLEAN DEFAULT true,
    owner_id INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 自习室成员表
CREATE TABLE IF NOT EXISTS study_room_members (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    room_id VARCHAR(36) REFERENCES study_rooms(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES "user"(id),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    left_at TIMESTAMP,
    total_study_time INTEGER DEFAULT 0, -- 总学习时间(分钟)
    status user_status DEFAULT 'active' NOT NULL,
    UNIQUE(room_id, user_id)
);

-- ==================== 软件更新模块 ====================

-- 软件版本表
CREATE TABLE IF NOT EXISTS app_versions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    version_name VARCHAR(50) NOT NULL,
    version_code INTEGER NOT NULL,
    platform VARCHAR(20) NOT NULL, -- android, ios, web
    download_url VARCHAR(500),
    file_size BIGINT, -- 文件大小(字节)
    file_hash VARCHAR(128), -- 文件哈希值
    release_notes TEXT,
    features TEXT[], -- 新功能列表
    bug_fixes TEXT[], -- 修复的问题
    is_force_update BOOLEAN DEFAULT false,
    min_supported_version VARCHAR(50),
    status post_status DEFAULT 'draft' NOT NULL,
    download_count INTEGER DEFAULT 0,
    published_at TIMESTAMP,
    created_by INTEGER REFERENCES "user"(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(platform, version_code)
);

-- 更新推送记录表
CREATE TABLE IF NOT EXISTS update_notifications (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    version_id VARCHAR(36) REFERENCES app_versions(id),
    user_id INTEGER REFERENCES "user"(id),
    notification_type VARCHAR(50) DEFAULT 'optional', -- force, recommended, optional
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    viewed_at TIMESTAMP,
    updated_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'sent', -- sent, viewed, updated, dismissed
    UNIQUE(version_id, user_id)
);

-- ==================== 学习分析模块 ====================

-- 学习统计表
CREATE TABLE IF NOT EXISTS learning_analytics (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    date DATE NOT NULL,
    study_time_minutes INTEGER DEFAULT 0,
    completed_lessons INTEGER DEFAULT 0,
    completed_exercises INTEGER DEFAULT 0,
    correct_answers INTEGER DEFAULT 0,
    total_answers INTEGER DEFAULT 0,
    accuracy_rate DECIMAL(5,4) DEFAULT 0.0000,
    focus_score DECIMAL(3,2) DEFAULT 0.00, -- 专注度评分
    subjects_studied TEXT[], -- 学习的科目
    learning_goals_achieved INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(user_id, date)
);

-- 学习目标表
CREATE TABLE IF NOT EXISTS learning_goals (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    goal_type VARCHAR(50) NOT NULL, -- daily, weekly, monthly, custom
    target_value DECIMAL(10,2) NOT NULL,
    current_value DECIMAL(10,2) DEFAULT 0.00,
    unit VARCHAR(50) NOT NULL, -- minutes, lessons, exercises, points
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_achieved BOOLEAN DEFAULT false,
    achieved_at TIMESTAMP,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
    status user_status DEFAULT 'active' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 权限管理模块 ====================

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT false, -- 系统内置角色
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL, -- user, course, community, system, etc.
    action VARCHAR(50) NOT NULL, -- create, read, update, delete, manage
    resource VARCHAR(100), -- 具体资源
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    role_id VARCHAR(36) REFERENCES roles(id) ON DELETE CASCADE,
    permission_id VARCHAR(36) REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    UNIQUE(role_id, permission_id)
);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id INTEGER REFERENCES "user"(id) ON DELETE CASCADE,
    role_id VARCHAR(36) REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by INTEGER REFERENCES "user"(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, role_id)
);

-- 创建索引
-- 用户相关索引
CREATE INDEX IF NOT EXISTS idx_user_id ON "user"(id);
CREATE INDEX IF NOT EXISTS idx_user_nickname ON "user"(nickname);
CREATE INDEX IF NOT EXISTS idx_user_email ON "user"(email);
CREATE INDEX IF NOT EXISTS idx_user_phone ON "user"(phone);
CREATE INDEX IF NOT EXISTS idx_user_status ON "user"(status);
CREATE INDEX IF NOT EXISTS idx_user_membership ON "user"(membership);
CREATE INDEX IF NOT EXISTS idx_user_level ON "user"(level);
CREATE INDEX IF NOT EXISTS idx_user_is_premium ON "user"(is_premium);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON "user"(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON admin_users(is_active);

-- 日志相关索引
CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_username ON login_logs(username);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_login_logs_ip_address ON login_logs(ip_address);

CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_action ON system_logs(action);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_module ON system_logs(module);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- 学习相关索引
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_android_id ON study_sessions(android_session_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_start_time ON study_sessions(start_time);
CREATE INDEX IF NOT EXISTS idx_study_sessions_session_type ON study_sessions(session_type);

CREATE INDEX IF NOT EXISTS idx_check_ins_user_id ON check_ins(user_id);
CREATE INDEX IF NOT EXISTS idx_check_ins_date ON check_ins(date);
CREATE INDEX IF NOT EXISTS idx_check_ins_android_id ON check_ins(android_checkin_id);

CREATE INDEX IF NOT EXISTS idx_ai_learning_records_user_id ON ai_learning_records(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_learning_records_android_id ON ai_learning_records(android_record_id);
CREATE INDEX IF NOT EXISTS idx_ai_learning_records_topic ON ai_learning_records(topic);
CREATE INDEX IF NOT EXISTS idx_ai_learning_records_created_at ON ai_learning_records(created_at);

-- 社区相关索引
CREATE INDEX IF NOT EXISTS idx_community_posts_author_id ON community_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_community_posts_android_id ON community_posts(android_post_id);
CREATE INDEX IF NOT EXISTS idx_community_posts_status ON community_posts(status);
CREATE INDEX IF NOT EXISTS idx_community_posts_category ON community_posts(category);
CREATE INDEX IF NOT EXISTS idx_community_posts_created_at ON community_posts(created_at);

CREATE INDEX IF NOT EXISTS idx_post_comments_post_id ON post_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_post_comments_author_id ON post_comments(author_id);
CREATE INDEX IF NOT EXISTS idx_post_comments_android_id ON post_comments(android_comment_id);
CREATE INDEX IF NOT EXISTS idx_post_comments_created_at ON post_comments(created_at);

-- 课程相关索引
CREATE INDEX IF NOT EXISTS idx_courses_android_id ON courses(android_course_id);
CREATE INDEX IF NOT EXISTS idx_courses_category ON courses(category);
CREATE INDEX IF NOT EXISTS idx_courses_is_active ON courses(is_active);
CREATE INDEX IF NOT EXISTS idx_courses_instructor ON courses(instructor);
CREATE INDEX IF NOT EXISTS idx_courses_created_at ON courses(created_at);

CREATE INDEX IF NOT EXISTS idx_course_enrollments_user_id ON course_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_android_id ON course_enrollments(android_enrollment_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_created_at ON course_enrollments(created_at);

-- 数据同步索引
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_type ON sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_table_name ON sync_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at);

-- 广告管理索引
CREATE INDEX IF NOT EXISTS idx_banners_status ON banners(status);
CREATE INDEX IF NOT EXISTS idx_banners_position ON banners(position);
CREATE INDEX IF NOT EXISTS idx_banners_sort_order ON banners(sort_order);
CREATE INDEX IF NOT EXISTS idx_banners_publish_start_time ON banners(publish_start_time);

CREATE INDEX IF NOT EXISTS idx_popups_status ON popups(status);
CREATE INDEX IF NOT EXISTS idx_popups_popup_type ON popups(popup_type);
CREATE INDEX IF NOT EXISTS idx_popups_publish_start_time ON popups(publish_start_time);

-- 题库管理索引
CREATE INDEX IF NOT EXISTS idx_question_categories_parent_id ON question_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_question_categories_subject ON question_categories(subject);
CREATE INDEX IF NOT EXISTS idx_question_categories_is_active ON question_categories(is_active);

CREATE INDEX IF NOT EXISTS idx_questions_category_id ON questions(category_id);
CREATE INDEX IF NOT EXISTS idx_questions_question_type ON questions(question_type);
CREATE INDEX IF NOT EXISTS idx_questions_subject ON questions(subject);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_questions_status ON questions(status);
CREATE INDEX IF NOT EXISTS idx_questions_review_status ON questions(review_status);
CREATE INDEX IF NOT EXISTS idx_questions_created_by ON questions(created_by);

-- 注意：exam_papers表不存在，已移除相关索引

CREATE INDEX IF NOT EXISTS idx_exam_paper_questions_paper_id ON exam_paper_questions(paper_id);
CREATE INDEX IF NOT EXISTS idx_exam_paper_questions_question_id ON exam_paper_questions(question_id);

-- 新课程管理索引
CREATE INDEX IF NOT EXISTS idx_course_categories_parent_id ON course_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_course_categories_is_active ON course_categories(is_active);

CREATE INDEX IF NOT EXISTS idx_instructors_user_id ON instructors(user_id);
CREATE INDEX IF NOT EXISTS idx_instructors_status ON instructors(status);
CREATE INDEX IF NOT EXISTS idx_instructors_is_verified ON instructors(is_verified);

CREATE INDEX IF NOT EXISTS idx_courses_new_category_id ON courses_new(category_id);
CREATE INDEX IF NOT EXISTS idx_courses_new_instructor_id ON courses_new(instructor_id);
CREATE INDEX IF NOT EXISTS idx_courses_new_status ON courses_new(status);
CREATE INDEX IF NOT EXISTS idx_courses_new_level ON courses_new(level);
CREATE INDEX IF NOT EXISTS idx_courses_new_is_featured ON courses_new(is_featured);
CREATE INDEX IF NOT EXISTS idx_courses_new_created_by ON courses_new(created_by);

CREATE INDEX IF NOT EXISTS idx_course_chapters_course_id ON course_chapters(course_id);
CREATE INDEX IF NOT EXISTS idx_course_lessons_chapter_id ON course_lessons(chapter_id);

-- 注意：course_reviews表不存在，已移除相关索引

CREATE INDEX IF NOT EXISTS idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_course_id ON learning_progress(course_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_lesson_id ON learning_progress(lesson_id);

-- 社区管理索引
CREATE INDEX IF NOT EXISTS idx_topics_is_hot ON topics(is_hot);
CREATE INDEX IF NOT EXISTS idx_topics_is_active ON topics(is_active);
CREATE INDEX IF NOT EXISTS idx_topics_created_by ON topics(created_by);

CREATE INDEX IF NOT EXISTS idx_tribes_owner_id ON tribes(owner_id);
CREATE INDEX IF NOT EXISTS idx_tribes_status ON tribes(status);
CREATE INDEX IF NOT EXISTS idx_tribes_is_public ON tribes(is_public);

CREATE INDEX IF NOT EXISTS idx_tribe_members_tribe_id ON tribe_members(tribe_id);
CREATE INDEX IF NOT EXISTS idx_tribe_members_user_id ON tribe_members(user_id);
CREATE INDEX IF NOT EXISTS idx_tribe_members_status ON tribe_members(status);

CREATE INDEX IF NOT EXISTS idx_mentorships_mentor_id ON mentorships(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorships_mentee_id ON mentorships(mentee_id);
CREATE INDEX IF NOT EXISTS idx_mentorships_status ON mentorships(status);

CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_activities_status ON activities(status);
CREATE INDEX IF NOT EXISTS idx_activities_start_time ON activities(start_time);
CREATE INDEX IF NOT EXISTS idx_activities_organizer_id ON activities(organizer_id);

CREATE INDEX IF NOT EXISTS idx_activity_participants_activity_id ON activity_participants(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON activity_participants(user_id);

CREATE INDEX IF NOT EXISTS idx_wishes_user_id ON wishes(user_id);
CREATE INDEX IF NOT EXISTS idx_wishes_category ON wishes(category);
CREATE INDEX IF NOT EXISTS idx_wishes_status ON wishes(status);

-- 心理咨询索引
-- 注意：counselors和consultations表不存在，已移除相关索引

-- 系统配置索引
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key);
CREATE INDEX IF NOT EXISTS idx_system_settings_is_public ON system_settings(is_public);

CREATE INDEX IF NOT EXISTS idx_ai_models_provider ON ai_models(provider);
CREATE INDEX IF NOT EXISTS idx_ai_models_is_active ON ai_models(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_models_is_default ON ai_models(is_default);

-- 学习资源索引
CREATE INDEX IF NOT EXISTS idx_learning_resources_resource_type ON learning_resources(resource_type);
CREATE INDEX IF NOT EXISTS idx_learning_resources_category ON learning_resources(category);
CREATE INDEX IF NOT EXISTS idx_learning_resources_subject ON learning_resources(subject);
CREATE INDEX IF NOT EXISTS idx_learning_resources_status ON learning_resources(status);
CREATE INDEX IF NOT EXISTS idx_learning_resources_uploaded_by ON learning_resources(uploaded_by);

-- 自习室索引
CREATE INDEX IF NOT EXISTS idx_study_rooms_room_type ON study_rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_study_rooms_owner_id ON study_rooms(owner_id);
CREATE INDEX IF NOT EXISTS idx_study_rooms_is_active ON study_rooms(is_active);

CREATE INDEX IF NOT EXISTS idx_study_room_members_room_id ON study_room_members(room_id);
CREATE INDEX IF NOT EXISTS idx_study_room_members_user_id ON study_room_members(user_id);
CREATE INDEX IF NOT EXISTS idx_study_room_members_status ON study_room_members(status);

-- 软件更新索引
CREATE INDEX IF NOT EXISTS idx_app_versions_platform ON app_versions(platform);
CREATE INDEX IF NOT EXISTS idx_app_versions_version_code ON app_versions(version_code);
CREATE INDEX IF NOT EXISTS idx_app_versions_status ON app_versions(status);
CREATE INDEX IF NOT EXISTS idx_app_versions_created_by ON app_versions(created_by);

CREATE INDEX IF NOT EXISTS idx_update_notifications_version_id ON update_notifications(version_id);
CREATE INDEX IF NOT EXISTS idx_update_notifications_user_id ON update_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_update_notifications_status ON update_notifications(status);

-- 学习分析索引
CREATE INDEX IF NOT EXISTS idx_learning_analytics_user_id ON learning_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_analytics_date ON learning_analytics(date);

CREATE INDEX IF NOT EXISTS idx_learning_goals_user_id ON learning_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_goals_goal_type ON learning_goals(goal_type);
CREATE INDEX IF NOT EXISTS idx_learning_goals_status ON learning_goals(status);
CREATE INDEX IF NOT EXISTS idx_learning_goals_start_date ON learning_goals(start_date);

-- 权限管理索引
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_is_active ON roles(is_active);

CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);
CREATE INDEX IF NOT EXISTS idx_permissions_module ON permissions(module);
CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions(action);

CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);

CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_is_active ON user_roles(is_active);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
-- 基础表触发器
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON "user" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_login_logs_updated_at BEFORE UPDATE ON login_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_logs_updated_at BEFORE UPDATE ON system_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 学习相关表触发器
CREATE TRIGGER update_study_sessions_updated_at BEFORE UPDATE ON study_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_check_ins_updated_at BEFORE UPDATE ON check_ins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_learning_records_updated_at BEFORE UPDATE ON ai_learning_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 社区相关表触发器
CREATE TRIGGER update_community_posts_updated_at BEFORE UPDATE ON community_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_post_comments_updated_at BEFORE UPDATE ON post_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 课程相关表触发器
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_enrollments_updated_at BEFORE UPDATE ON course_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 数据同步表触发器
CREATE TRIGGER update_sync_logs_updated_at BEFORE UPDATE ON sync_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 广告管理表触发器
CREATE TRIGGER update_banners_updated_at BEFORE UPDATE ON banners FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_popups_updated_at BEFORE UPDATE ON popups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 题目管理表触发器
CREATE TRIGGER update_question_categories_updated_at BEFORE UPDATE ON question_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_questions_updated_at BEFORE UPDATE ON questions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_exam_paper_questions_updated_at BEFORE UPDATE ON exam_paper_questions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- 注意：exam_papers表不存在，已移除相关触发器

-- 课程管理表触发器
CREATE TRIGGER update_course_categories_updated_at BEFORE UPDATE ON course_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_instructors_updated_at BEFORE UPDATE ON instructors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_new_updated_at BEFORE UPDATE ON courses_new FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_chapters_updated_at BEFORE UPDATE ON course_chapters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_lessons_updated_at BEFORE UPDATE ON course_lessons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- 注意：course_reviews表不存在，已移除相关触发器

-- 学习进度表触发器
CREATE TRIGGER update_learning_progress_updated_at BEFORE UPDATE ON learning_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_resources_updated_at BEFORE UPDATE ON learning_resources FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 社区管理表触发器
CREATE TRIGGER update_topics_updated_at BEFORE UPDATE ON topics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tribes_updated_at BEFORE UPDATE ON tribes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tribe_members_updated_at BEFORE UPDATE ON tribe_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_mentorships_updated_at BEFORE UPDATE ON mentorships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_activity_participants_updated_at BEFORE UPDATE ON activity_participants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wishes_updated_at BEFORE UPDATE ON wishes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 心理咨询表触发器
-- 注意：counselors和consultations表不存在，已移除相关触发器

-- 系统配置表触发器
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_models_updated_at BEFORE UPDATE ON ai_models FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自习室表触发器
CREATE TRIGGER update_study_rooms_updated_at BEFORE UPDATE ON study_rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_study_room_members_updated_at BEFORE UPDATE ON study_room_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 软件更新表触发器
CREATE TRIGGER update_app_versions_updated_at BEFORE UPDATE ON app_versions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_update_notifications_updated_at BEFORE UPDATE ON update_notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 学习分析表触发器
CREATE TRIGGER update_learning_analytics_updated_at BEFORE UPDATE ON learning_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_goals_updated_at BEFORE UPDATE ON learning_goals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 权限管理表触发器
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON role_permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户（密码：admin123）
INSERT INTO admin_users (username, email, hashed_password, full_name, role, is_superuser, is_active)
VALUES (
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e',
    '系统管理员',
    'SUPERADMIN',
    true,
    true
) ON CONFLICT (username) DO NOTHING;

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as status;
