{"name": "wiscude-admin-frontend", "version": "1.0.0", "description": "WisCude 后台管理系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.5", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-plus": "^2.4.4", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^6.7.3", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.5", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "sass": "^1.70.0", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.12", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=18.0.0"}}