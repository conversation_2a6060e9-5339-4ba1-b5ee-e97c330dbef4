<template>
  <div class="tabs-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>标签页系统测试</span>
        </div>
      </template>
      
      <div class="test-content">
        <h3>当前标签信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标签ID">
            {{ tabsStore.activeTab?.id || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="标签标题">
            {{ tabsStore.activeTab?.title || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="路径">
            {{ tabsStore.activeTab?.path || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="是否可关闭">
            {{ tabsStore.activeTab?.closable ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="是否缓存">
            {{ tabsStore.activeTab?.cached ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ tabsStore.activeTab ? new Date(tabsStore.activeTab.timestamp).toLocaleString() : '无' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">标签统计</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总标签数" :value="tabsStore.tabsCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="缓存页面数" :value="tabsStore.cachedViewNames.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最大标签数" :value="tabsStore.maxTabs" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最大缓存数" :value="tabsStore.maxCachedViews" />
          </el-col>
        </el-row>
        
        <h3 style="margin-top: 20px;">测试操作</h3>
        <el-space wrap>
          <el-button type="primary" @click="openRandomTab">
            打开随机标签
          </el-button>
          <el-button type="warning" @click="refreshCurrentTab">
            刷新当前标签
          </el-button>
          <el-button type="danger" @click="closeCurrentTab">
            关闭当前标签
          </el-button>
          <el-button @click="clearAllTabs">
            清空所有标签
          </el-button>
        </el-space>
        
        <h3 style="margin-top: 20px;">所有标签列表</h3>
        <el-table :data="tabsStore.tabs" style="width: 100%">
          <el-table-column prop="title" label="标题" />
          <el-table-column prop="path" label="路径" />
          <el-table-column prop="closable" label="可关闭">
            <template #default="{ row }">
              <el-tag :type="row.closable ? 'success' : 'danger'">
                {{ row.closable ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="cached" label="缓存">
            <template #default="{ row }">
              <el-tag :type="row.cached ? 'success' : 'info'">
                {{ row.cached ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button size="small" @click="activateTab(row.id)">
                激活
              </el-button>
              <el-button 
                v-if="row.closable" 
                size="small" 
                type="danger" 
                @click="closeTab(row.id)"
              >
                关闭
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTabsStore } from '@/stores/tabs'
import { ElMessage } from 'element-plus'

const router = useRouter()
const tabsStore = useTabsStore()

// 测试路由列表
const testRoutes = [
  '/dashboard',
  '/users',
  '/settings',
  '/profile',
  '/system-monitoring/overview',
  '/data-management/analysis',
  '/community/overview'
]

// 打开随机标签
const openRandomTab = () => {
  const randomRoute = testRoutes[Math.floor(Math.random() * testRoutes.length)]
  router.push(randomRoute)
  ElMessage.success(`打开标签: ${randomRoute}`)
}

// 刷新当前标签
const refreshCurrentTab = () => {
  if (tabsStore.activeTab) {
    tabsStore.refreshTab(tabsStore.activeTab.id)
    ElMessage.success('标签已刷新')
  } else {
    ElMessage.warning('没有激活的标签')
  }
}

// 关闭当前标签
const closeCurrentTab = () => {
  if (tabsStore.activeTab) {
    if (tabsStore.activeTab.closable) {
      tabsStore.removeTab(tabsStore.activeTab.id)
      ElMessage.success('标签已关闭')
    } else {
      ElMessage.warning('当前标签不可关闭')
    }
  } else {
    ElMessage.warning('没有激活的标签')
  }
}

// 清空所有标签
const clearAllTabs = () => {
  tabsStore.closeAllTabs()
  ElMessage.success('所有可关闭的标签已清空')
}

// 激活标签
const activateTab = (tabId: string) => {
  tabsStore.activateTab(tabId)
  ElMessage.success('标签已激活')
}

// 关闭标签
const closeTab = (tabId: string) => {
  tabsStore.removeTab(tabId)
  ElMessage.success('标签已关闭')
}
</script>

<style lang="scss" scoped>
.tabs-test {
  padding: 20px;
  
  .test-content {
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
