/**
 * 标签页状态管理
 * 管理浏览器标签栏式的页面导航和缓存
 */

import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

// 标签项接口
export interface TabItem {
  id: string
  title: string
  path: string
  name: string
  icon?: string
  closable: boolean
  cached: boolean
  params?: Record<string, any>
  query?: Record<string, any>
  meta?: Record<string, any>
  timestamp: number // 用于LRU算法
}

// 右键菜单选项
export interface ContextMenuOption {
  key: string
  label: string
  icon: string
  disabled?: boolean
  divided?: boolean
}

export const useTabsStore = defineStore('tabs', () => {
  // 状态
  const tabs = ref<TabItem[]>([])
  const activeTabId = ref<string>('')
  const cachedViews = ref<Set<string>>(new Set())
  const maxTabs = ref(20)
  const maxCachedViews = ref(15)

  // 计算属性
  const activeTab = computed(() => 
    tabs.value.find(tab => tab.id === activeTabId.value)
  )

  const tabsCount = computed(() => tabs.value.length)

  const canAddTab = computed(() => tabsCount.value < maxTabs.value)

  const cachedViewNames = computed(() => Array.from(cachedViews.value))

  // 生成标签ID
  const generateTabId = (route: RouteLocationNormalized): string => {
    const { path, params, query } = route
    const paramStr = Object.keys(params).length ? JSON.stringify(params) : ''
    const queryStr = Object.keys(query).length ? JSON.stringify(query) : ''
    return `${path}${paramStr}${queryStr}`
  }

  // 生成标签标题
  const generateTabTitle = (route: RouteLocationNormalized): string => {
    return (route.meta?.title as string) || route.name?.toString() || '未知页面'
  }

  // 检查标签是否存在
  const hasTab = (tabId: string): boolean => {
    return tabs.value.some(tab => tab.id === tabId)
  }

  // 添加标签
  const addTab = (route: RouteLocationNormalized): TabItem => {
    try {
      const tabId = generateTabId(route)

      // 如果标签已存在，直接激活
      const existingTab = tabs.value.find(tab => tab.id === tabId)
      if (existingTab) {
        activeTabId.value = tabId
        existingTab.timestamp = Date.now()
        return existingTab
      }

      // 检查是否超出最大标签数
      if (!canAddTab.value) {
        // 移除最久未访问的标签
        removeOldestTab()
      }

    // 创建新标签
    const newTab: TabItem = {
      id: tabId,
      title: generateTabTitle(route),
      path: route.path,
      name: route.name?.toString() || '',
      icon: route.meta?.icon as string,
      closable: route.meta?.closable !== false, // 默认可关闭
      cached: route.meta?.keepAlive !== false, // 默认缓存
      params: { ...route.params },
      query: { ...route.query },
      meta: { ...route.meta },
      timestamp: Date.now()
    }

    tabs.value.push(newTab)
    activeTabId.value = tabId

    // 添加到缓存
    if (newTab.cached && newTab.name) {
      addToCache(newTab.name)
    }

      saveTabsToStorage()
      return newTab
    } catch (error) {
      console.error('添加标签失败:', error)
      // 返回一个默认标签
      return {
        id: 'error-tab',
        title: '页面加载失败',
        path: route.path,
        name: route.name?.toString() || '',
        closable: true,
        cached: false,
        timestamp: Date.now()
      }
    }
  }

  // 移除标签
  const removeTab = (tabId: string): void => {
    const tabIndex = tabs.value.findIndex(tab => tab.id === tabId)
    if (tabIndex === -1) return

    const tab = tabs.value[tabIndex]
    
    // 从缓存中移除
    if (tab.name) {
      removeFromCache(tab.name)
    }

    // 移除标签
    tabs.value.splice(tabIndex, 1)

    // 如果移除的是当前激活标签，需要激活其他标签
    if (activeTabId.value === tabId) {
      if (tabs.value.length > 0) {
        // 激活相邻标签
        const nextIndex = Math.min(tabIndex, tabs.value.length - 1)
        activeTabId.value = tabs.value[nextIndex].id
      } else {
        activeTabId.value = ''
      }
    }

    saveTabsToStorage()
  }

  // 移除最久未访问的标签
  const removeOldestTab = (): void => {
    if (tabs.value.length === 0) return

    // 找到最久未访问且可关闭的标签
    const closableTabs = tabs.value.filter(tab => tab.closable)
    if (closableTabs.length === 0) return

    const oldestTab = closableTabs.reduce((oldest, current) => 
      current.timestamp < oldest.timestamp ? current : oldest
    )

    removeTab(oldestTab.id)
  }

  // 激活标签
  const activateTab = (tabId: string): void => {
    const tab = tabs.value.find(tab => tab.id === tabId)
    if (!tab) return

    activeTabId.value = tabId
    tab.timestamp = Date.now()
    saveTabsToStorage()
  }

  // 关闭其他标签
  const closeOtherTabs = (keepTabId: string): void => {
    const keepTab = tabs.value.find(tab => tab.id === keepTabId)
    if (!keepTab) return

    // 保留不可关闭的标签和指定标签
    const tabsToKeep = tabs.value.filter(tab => 
      !tab.closable || tab.id === keepTabId
    )

    // 清理缓存
    tabs.value.forEach(tab => {
      if (tab.closable && tab.id !== keepTabId && tab.name) {
        removeFromCache(tab.name)
      }
    })

    tabs.value = tabsToKeep
    activeTabId.value = keepTabId
    saveTabsToStorage()
  }

  // 关闭所有标签
  const closeAllTabs = (): void => {
    // 保留不可关闭的标签
    const tabsToKeep = tabs.value.filter(tab => !tab.closable)

    // 清理缓存
    tabs.value.forEach(tab => {
      if (tab.closable && tab.name) {
        removeFromCache(tab.name)
      }
    })

    tabs.value = tabsToKeep
    
    if (tabsToKeep.length > 0) {
      activeTabId.value = tabsToKeep[0].id
    } else {
      activeTabId.value = ''
    }

    saveTabsToStorage()
  }

  // 刷新标签
  const refreshTab = (tabId: string): void => {
    const tab = tabs.value.find(tab => tab.id === tabId)
    if (!tab || !tab.name) return

    // 从缓存中移除，强制重新加载
    removeFromCache(tab.name)
    
    // 延迟重新添加到缓存
    nextTick(() => {
      if (tab.cached) {
        addToCache(tab.name)
      }
    })
  }

  // 添加到缓存
  const addToCache = (viewName: string): void => {
    if (cachedViews.value.has(viewName)) return

    // 检查缓存数量限制
    if (cachedViews.value.size >= maxCachedViews.value) {
      // 移除最久未访问的缓存
      removeOldestCache()
    }

    cachedViews.value.add(viewName)
  }

  // 从缓存中移除
  const removeFromCache = (viewName: string): void => {
    cachedViews.value.delete(viewName)
  }

  // 移除最久未访问的缓存
  const removeOldestCache = (): void => {
    const cachedTabs = tabs.value.filter(tab => 
      tab.cached && tab.name && cachedViews.value.has(tab.name)
    )

    if (cachedTabs.length === 0) return

    const oldestCachedTab = cachedTabs.reduce((oldest, current) => 
      current.timestamp < oldest.timestamp ? current : oldest
    )

    if (oldestCachedTab.name) {
      removeFromCache(oldestCachedTab.name)
    }
  }

  // 更新标签顺序（拖拽排序）
  const updateTabsOrder = (newTabs: TabItem[]): void => {
    tabs.value = newTabs
    saveTabsToStorage()
  }

  // 保存到本地存储
  const saveTabsToStorage = (): void => {
    try {
      const tabsData = {
        tabs: tabs.value,
        activeTabId: activeTabId.value,
        cachedViews: Array.from(cachedViews.value)
      }
      localStorage.setItem('wiscude-tabs', JSON.stringify(tabsData))
    } catch (error) {
      console.error('保存标签状态失败:', error)
    }
  }

  // 从本地存储恢复
  const restoreTabsFromStorage = (): void => {
    try {
      const stored = localStorage.getItem('wiscude-tabs')
      if (!stored) return

      const tabsData = JSON.parse(stored)
      if (tabsData.tabs && Array.isArray(tabsData.tabs)) {
        tabs.value = tabsData.tabs
        activeTabId.value = tabsData.activeTabId || ''
        cachedViews.value = new Set(tabsData.cachedViews || [])
      }
    } catch (error) {
      console.error('恢复标签状态失败:', error)
      clearTabsStorage()
    }
  }

  // 清空本地存储
  const clearTabsStorage = (): void => {
    localStorage.removeItem('wiscude-tabs')
    tabs.value = []
    activeTabId.value = ''
    cachedViews.value.clear()
  }

  // 获取右键菜单选项
  const getContextMenuOptions = (tabId: string): ContextMenuOption[] => {
    const tab = tabs.value.find(t => t.id === tabId)
    if (!tab) return []

    const options: ContextMenuOption[] = [
      {
        key: 'refresh',
        label: '刷新页面',
        icon: 'Refresh'
      },
      {
        key: 'close',
        label: '关闭标签',
        icon: 'Close',
        disabled: !tab.closable
      },
      {
        key: 'close-others',
        label: '关闭其他',
        icon: 'SemiSelect',
        divided: true,
        disabled: tabs.value.filter(t => t.closable && t.id !== tabId).length === 0
      },
      {
        key: 'close-all',
        label: '关闭所有',
        icon: 'CircleClose',
        disabled: tabs.value.filter(t => t.closable).length === 0
      }
    ]

    return options
  }

  return {
    // 状态
    tabs,
    activeTabId,
    cachedViews,
    maxTabs,
    maxCachedViews,

    // 计算属性
    activeTab,
    tabsCount,
    canAddTab,
    cachedViewNames,

    // 方法
    generateTabId,
    generateTabTitle,
    hasTab,
    addTab,
    removeTab,
    removeOldestTab,
    activateTab,
    closeOtherTabs,
    closeAllTabs,
    refreshTab,
    addToCache,
    removeFromCache,
    removeOldestCache,
    updateTabsOrder,
    saveTabsToStorage,
    restoreTabsFromStorage,
    clearTabsStorage,
    getContextMenuOptions
  }
})

export default useTabsStore
