<template>
  <div class="tabs-bar">
    <div class="tabs-container" ref="tabsContainer">
      <VueDraggable
        v-model="draggableTabs"
        :animation="200"
        ghost-class="tab-ghost"
        chosen-class="tab-chosen"
        drag-class="tab-drag"
        @start="onDragStart"
        @end="onDragEnd"
        item-key="id"
        class="tabs-draggable"
      >
        <template #item="{ element: tab }">
          <div
            :class="[
              'tab-item',
              { 'active': tab.id === tabsStore.activeTabId }
            ]"
            @click="handleTabClick(tab.id)"
            @contextmenu.prevent="handleContextMenu($event, tab.id)"
          >
            <el-icon v-if="tab.icon" class="tab-icon">
              <component :is="tab.icon" />
            </el-icon>
            <span class="tab-title">{{ tab.title }}</span>
            <el-icon
              v-if="tab.closable"
              class="tab-close"
              @click.stop="handleTabClose(tab.id)"
            >
              <Close />
            </el-icon>
          </div>
        </template>
      </VueDraggable>
    </div>
    
    <!-- 右键菜单 -->
    <div 
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      @click.stop
    >
      <div 
        v-for="option in contextMenuOptions"
        :key="option.key"
        :class="[
          'context-menu-item',
          { 'disabled': option.disabled, 'divided': option.divided }
        ]"
        @click="handleContextMenuClick(option.key)"
      >
        <el-icon class="menu-icon">
          <component :is="option.icon" />
        </el-icon>
        <span>{{ option.label }}</span>
      </div>
    </div>
    
    <!-- 遮罩层，用于关闭右键菜单 -->
    <div 
      v-if="contextMenu.visible"
      class="context-menu-overlay"
      @click="hideContextMenu"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTabsStore } from '@/stores/tabs'
import type { ContextMenuOption, TabItem } from '@/stores/tabs'
import { VueDraggable } from 'vue-draggable-plus'

const router = useRouter()
const tabsStore = useTabsStore()

// 右键菜单状态
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  tabId: ''
})

// 标签容器引用
const tabsContainer = ref<HTMLElement>()

// 拖拽状态
const isDragging = ref(false)

// 可拖拽的标签列表
const draggableTabs = computed({
  get: () => tabsStore.tabs,
  set: (newTabs: TabItem[]) => {
    tabsStore.updateTabsOrder(newTabs)
  }
})

// 计算右键菜单选项
const contextMenuOptions = computed((): ContextMenuOption[] => {
  if (!contextMenu.value.tabId) return []
  return tabsStore.getContextMenuOptions(contextMenu.value.tabId)
})

// 拖拽开始
const onDragStart = () => {
  isDragging.value = true
}

// 拖拽结束
const onDragEnd = () => {
  isDragging.value = false
}

// 处理标签点击
const handleTabClick = (tabId: string) => {
  // 如果正在拖拽，不处理点击事件
  if (isDragging.value) return

  const tab = tabsStore.tabs.find(t => t.id === tabId)
  if (!tab) return

  // 激活标签
  tabsStore.activateTab(tabId)

  // 跳转到对应路由
  router.push({
    path: tab.path,
    params: tab.params,
    query: tab.query
  }).catch(error => {
    if (!error.message.includes('Avoided redundant navigation')) {
      console.error('标签跳转失败:', error)
    }
  })
}

// 处理标签关闭
const handleTabClose = (tabId: string) => {
  const tab = tabsStore.tabs.find(t => t.id === tabId)
  if (!tab || !tab.closable) return

  tabsStore.removeTab(tabId)
  
  // 如果关闭的是当前标签，需要跳转到新的激活标签
  if (tabId === tabsStore.activeTabId && tabsStore.activeTab) {
    router.push({
      path: tabsStore.activeTab.path,
      params: tabsStore.activeTab.params,
      query: tabsStore.activeTab.query
    }).catch(error => {
      if (!error.message.includes('Avoided redundant navigation')) {
        console.error('标签关闭后跳转失败:', error)
      }
    })
  }
}

// 处理右键菜单
const handleContextMenu = (event: MouseEvent, tabId: string) => {
  event.preventDefault()
  
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    tabId
  }
}

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.visible = false
}

// 处理右键菜单点击
const handleContextMenuClick = (action: string) => {
  const tabId = contextMenu.value.tabId
  if (!tabId) return

  switch (action) {
    case 'refresh':
      tabsStore.refreshTab(tabId)
      // 触发页面刷新事件，由TabsContainer处理
      break
    case 'close':
      handleTabClose(tabId)
      break
    case 'close-others':
      tabsStore.closeOtherTabs(tabId)
      // 如果当前激活的不是保留的标签，需要跳转
      if (tabsStore.activeTab && tabsStore.activeTab.id !== tabId) {
        const keepTab = tabsStore.tabs.find(t => t.id === tabId)
        if (keepTab) {
          router.push({
            path: keepTab.path,
            params: keepTab.params,
            query: keepTab.query
          }).catch(error => {
            if (!error.message.includes('Avoided redundant navigation')) {
              console.error('关闭其他标签后跳转失败:', error)
            }
          })
        }
      }
      break
    case 'close-all':
      tabsStore.closeAllTabs()
      // 跳转到首页或剩余的第一个标签
      if (tabsStore.activeTab) {
        router.push({
          path: tabsStore.activeTab.path,
          params: tabsStore.activeTab.params,
          query: tabsStore.activeTab.query
        }).catch(error => {
          if (!error.message.includes('Avoided redundant navigation')) {
            console.error('关闭所有标签后跳转失败:', error)
          }
        })
      } else {
        router.push('/dashboard').catch(error => {
          if (!error.message.includes('Avoided redundant navigation')) {
            console.error('关闭所有标签后跳转到首页失败:', error)
          }
        })
      }
      break
  }
  
  hideContextMenu()
}

// 监听全局点击事件，关闭右键菜单
const handleGlobalClick = () => {
  if (contextMenu.value.visible) {
    hideContextMenu()
  }
}

// 监听键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && contextMenu.value.visible) {
    hideContextMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.tabs-bar {
  height: 100%;
  position: relative;
  background: white;

  // 响应式设计
  @media (max-width: 768px) {
    .tabs-container {
      padding: 0 5px;

      .tab-item {
        min-width: 60px;
        max-width: 120px;
        padding: 0 8px;

        .tab-title {
          font-size: 11px;
        }

        .tab-close {
          font-size: 10px;
        }
      }
    }
  }
  
  .tabs-container {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 10px;
    overflow-x: auto;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      height: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .tabs-draggable {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  
  .tab-item {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 12px;
    margin-right: 4px;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    transition: all 0.3s;
    min-width: 80px;
    max-width: 200px;
    
    &:hover {
      background: #ecf5ff;
      border-color: #b3d8ff;
    }
    
    &.active {
      background: #409eff;
      border-color: #409eff;
      color: white;
      
      .tab-icon,
      .tab-title,
      .tab-close {
        color: white;
      }
    }
    
    .tab-icon {
      font-size: 14px;
      margin-right: 6px;
      flex-shrink: 0;
    }
    
    .tab-title {
      flex: 1;
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .tab-close {
      font-size: 12px;
      margin-left: 6px;
      padding: 2px;
      border-radius: 2px;
      flex-shrink: 0;
      opacity: 0.7;
      transition: all 0.2s;
      
      &:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.1);
      }
    }
    
    &.active .tab-close:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  // 拖拽状态样式
  .tab-ghost {
    opacity: 0.5;
  }

  .tab-chosen {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .tab-drag {
    transform: rotate(5deg);
    opacity: 0.8;
  }
}

.context-menu {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 4px 0;
  min-width: 120px;
  
  .context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover:not(.disabled) {
      background: #f5f7fa;
    }
    
    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }
    
    &.divided {
      border-top: 1px solid #e4e7ed;
      margin-top: 4px;
      padding-top: 8px;
    }
    
    .menu-icon {
      font-size: 14px;
      margin-right: 8px;
    }
  }
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}
</style>
